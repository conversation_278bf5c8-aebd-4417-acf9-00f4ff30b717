package com.moregames.playtime.carousel

import com.moregames.base.db.OptimisticLockException
import com.moregames.playtime.carousel.UserCarouselTaskState.*
import com.moregames.playtime.carousel.domain.TaskDefinition
import com.moregames.playtime.carousel.domain.UserCarouselTask
import com.moregames.playtime.carousel.domain.UserCarouselTaskEntity
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CarouselService @Inject constructor(
  private val carouselPersistenceService: CarouselPersistenceService,
) {
  suspend fun findActiveUserTask(userId: String, gameId: Int): UserCarouselTask.InProgress? {
    return carouselPersistenceService.findActiveCarouselTaskPerGame(userId, gameId)
      ?.toUserCarouselTask() as? UserCarouselTask.InProgress
  }

  suspend fun countUnclaimedTasks(userId: String): Long {
    return carouselPersistenceService.countUnclaimedTasks(userId)
  }

  suspend fun updateTaskProgress(task: UserCarouselTask.InProgress, progress: Int, achievement: String?): UserCarouselTask {
    val updated = carouselPersistenceService.updateTaskProgress(task.taskId, task.progress, progress, achievement)
    if (!updated) throw OptimisticLockException("Carousel task ${task.taskId} update failed due to optimistic locking")

    return carouselPersistenceService.getUserCarouselTask(task.taskId).toUserCarouselTask()
  }

  suspend fun markTaskAsFinished(taskId: UUID) {
    carouselPersistenceService.markTaskAsFinished(taskId)
  }

  private fun UserCarouselTaskEntity.toUserCarouselTask(): UserCarouselTask {
    val definition = TaskDefinition(
      gameId = gameId,
      titleTranslation = titleTranslation,
      icon = icon,
      progressMax = progressMax,
      goal = goal,
      order = order,
      calculator = calculator,
      enabled = enabled,
    )
    return when (state) {
      NEW -> UserCarouselTask.New(taskId, userId, definition)
      IN_PROGRESS -> UserCarouselTask.InProgress(taskId, userId, definition, progress, achievement)
      UNCLAIMED -> UserCarouselTask.Unclaimed(taskId, userId, definition)
      CLAIMED -> UserCarouselTask.Claimed(taskId, userId, definition, blockedUntil!!)
      COMPLETED -> UserCarouselTask.Completed(taskId, userId, definition)
    }
  }
}
