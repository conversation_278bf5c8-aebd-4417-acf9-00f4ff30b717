package com.moregames.playtime.carousel

import com.moregames.base.bus.ImmediateRetries
import com.moregames.base.bus.Message
import com.moregames.base.bus.MessageBus
import com.moregames.base.bus.MessageHandler
import com.moregames.base.dto.AppPlatform
import com.moregames.base.messaging.dto.UserChallengeProgressDto
import com.moregames.base.util.alert
import com.moregames.base.util.logger
import com.moregames.playtime.carousel.domain.UserCarouselTask
import com.moregames.playtime.games.GamesService
import com.moregames.playtime.user.challenge.progress.ObjectiveProgressCalculator
import com.moregames.playtime.user.objectives.CalculatedObjectiveProgress
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.min

@Singleton
class CarouselProgressTrackingHandler @Inject constructor(
  private val carouselService: CarouselService,
  private val gamesService: GamesService,
  private val calculators: Map<String, ObjectiveProgressCalculator>,
  private val messageBus: MessageBus,
) {

  @MessageHandler
  @ImmediateRetries(2)
  suspend fun handleUserCarouselProgress(command: HandleCarouselProgressCommand) = with(command) {
    val gameId = gamesService.getGameId(userChallengeProgressDto.userId, AppPlatform.ANDROID) ?: return
    val task = carouselService.findActiveUserTask(userChallengeProgressDto.userId, gameId) ?: return
    val calculator = findProgressCalculator(task) ?: return

    val newProgress = calculator.calculateProgress(userChallengeProgressDto, task)
      .let { capProgress(it, task) }

    if (newProgress.progress <= task.progress) return

    val updatedTask = carouselService.updateTaskProgress(task, newProgress.progress, newProgress.achievement)
    if (updatedTask is UserCarouselTask.InProgress && updatedTask.progress == updatedTask.definition.progressMax) {
      carouselService.markTaskAsFinished(updatedTask.taskId)
      messageBus.publish(CarouselTaskFinishedEvent(updatedTask.taskId, updatedTask.userId))
    }
  }

  private fun findProgressCalculator(task: UserCarouselTask): ObjectiveProgressCalculator? {
    val progressCalculator = calculators[task.definition.calculator.name]
    if (progressCalculator == null) {
      logger().alert("Not found progress CALCULATOR for task ${task.taskId}")
    }
    return progressCalculator
  }

  private fun capProgress(
    newProgress: CalculatedObjectiveProgress,
    task: UserCarouselTask
  ) = newProgress.copy(progress = min(newProgress.progress, task.definition.progressMax))
}

data class HandleCarouselProgressCommand(
  val userChallengeProgressDto: UserChallengeProgressDto
) : Message