package com.moregames.playtime.carousel

import assertk.assertThat
import assertk.assertions.isEqualTo
import assertk.assertions.isInstanceOf
import assertk.assertions.isNull
import com.moregames.base.db.OptimisticLockException
import com.moregames.base.junit.MockExtension
import com.moregames.base.util.mock
import com.moregames.playtime.carousel.UserCarouselTaskState.*
import com.moregames.playtime.carousel.domain.TaskDefinition
import com.moregames.playtime.carousel.domain.UserCarouselTask
import com.moregames.playtime.carousel.domain.UserCarouselTaskEntity
import com.moregames.playtime.user.challenge.progress.ObjectiveProgressCalculatorType
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.verifyBlocking
import java.time.Instant
import java.util.*
import kotlin.test.assertFailsWith

@ExtendWith(MockExtension::class)
class CarouselServiceTest(
  private val carouselPersistenceService: CarouselPersistenceService,
) {
  private val underTest = CarouselService(carouselPersistenceService)

  private companion object {
    const val USER_ID = "user123"
    const val GAME_ID = 1001
    val TASK_ID = UUID.randomUUID()
    val BLOCKED_UNTIL = Instant.parse("2025-01-01T12:00:00Z")

    val taskDefinition = TaskDefinition(
      gameId = GAME_ID,
      titleTranslation = "Complete 5 levels",
      icon = "level_icon",
      progressMax = 100,
      goal = 5,
      order = 1,
      calculator = ObjectiveProgressCalculatorType.MILESTONE,
      enabled = true,
    )
  }

  @Test
  fun `SHOULD return InProgress task ON findActiveUserTask WHEN active task exists and is InProgress`() = runBlocking {
    val taskEntity = createTaskEntity(state = IN_PROGRESS, progress = 50, achievement = "level_3")
    carouselPersistenceService.mock({ findActiveCarouselTaskPerGame(USER_ID, GAME_ID) }, taskEntity)

    val result = underTest.findActiveUserTask(USER_ID, GAME_ID)

    assertThat(result).isInstanceOf(UserCarouselTask.InProgress::class)
    assertThat(result!!.taskId).isEqualTo(TASK_ID)
    assertThat(result.userId).isEqualTo(USER_ID)
    assertThat(result.progress).isEqualTo(50)
    assertThat(result.achievement).isEqualTo("level_3")
    assertThat(result.definition).isEqualTo(taskDefinition)
    // Verify objective property is correctly set
    assertThat(result.objective.progressMax).isEqualTo(taskDefinition.progressMax)
    assertThat(result.objective.goal).isEqualTo(taskDefinition.goal)
  }

  @Test
  fun `SHOULD return null ON findActiveUserTask WHEN active task exists but is not InProgress`() = runBlocking {
    val taskEntity = createTaskEntity(state = NEW)
    carouselPersistenceService.mock({ findActiveCarouselTaskPerGame(USER_ID, GAME_ID) }, taskEntity)

    val result = underTest.findActiveUserTask(USER_ID, GAME_ID)

    assertThat(result).isNull()
  }

  @Test
  fun `SHOULD return null ON findActiveUserTask WHEN no active task exists`() = runBlocking {
    carouselPersistenceService.mock({ findActiveCarouselTaskPerGame(USER_ID, GAME_ID) }, null)

    val result = underTest.findActiveUserTask(USER_ID, GAME_ID)

    assertThat(result).isNull()
  }

  @Test
  fun `SHOULD return correct count ON countUnclaimedTasks`() = runBlocking {
    carouselPersistenceService.mock({ countUnclaimedTasks(USER_ID) }, 3L)

    val result = underTest.countUnclaimedTasks(USER_ID)

    assertThat(result).isEqualTo(3L)
  }

  @Test
  fun `SHOULD return zero ON countUnclaimedTasks WHEN no unclaimed tasks exist`() = runBlocking {
    carouselPersistenceService.mock({ countUnclaimedTasks(USER_ID) }, 0L)

    val result = underTest.countUnclaimedTasks(USER_ID)

    assertThat(result).isEqualTo(0L)
  }

  @Test
  fun `SHOULD update task progress and return updated task ON updateTaskProgress WHEN update succeeds`() = runBlocking {
    val inProgressTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 50, "level_3")
    val updatedTaskEntity = createTaskEntity(state = IN_PROGRESS, progress = 75, achievement = "level_4")

    carouselPersistenceService.mock({ updateTaskProgress(TASK_ID, 50, 75, "level_4") }, true)
    carouselPersistenceService.mock({ getUserCarouselTask(TASK_ID) }, updatedTaskEntity)

    val result = underTest.updateTaskProgress(inProgressTask, 75, "level_4")

    assertThat(result).isInstanceOf(UserCarouselTask.InProgress::class)
    val updatedTask = result as UserCarouselTask.InProgress
    assertThat(updatedTask.progress).isEqualTo(75)
    assertThat(updatedTask.achievement).isEqualTo("level_4")
  }

  @Test
  fun `SHOULD throw OptimisticLockException ON updateTaskProgress WHEN update fails`() = runBlocking {
    val inProgressTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 50, "level_3")

    carouselPersistenceService.mock({ updateTaskProgress(TASK_ID, 50, 75, "level_4") }, false)

    val exception = assertFailsWith<OptimisticLockException> {
      underTest.updateTaskProgress(inProgressTask, 75, "level_4")
    }

    assertThat(exception.message).isEqualTo("Carousel task $TASK_ID update failed due to optimistic locking")
  }

  @Test
  fun `SHOULD update task progress with null achievement ON updateTaskProgress`() = runBlocking {
    val inProgressTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 25, null)
    val updatedTaskEntity = createTaskEntity(state = IN_PROGRESS, progress = 50, achievement = null)

    carouselPersistenceService.mock({ updateTaskProgress(TASK_ID, 25, 50, null) }, true)
    carouselPersistenceService.mock({ getUserCarouselTask(TASK_ID) }, updatedTaskEntity)

    val result = underTest.updateTaskProgress(inProgressTask, 50, null)

    assertThat(result).isInstanceOf(UserCarouselTask.InProgress::class)
    val updatedTask = result as UserCarouselTask.InProgress
    assertThat(updatedTask.progress).isEqualTo(50)
    assertThat(updatedTask.achievement).isNull()
  }

  @Test
  fun `SHOULD call persistence service ON markTaskAsFinished`() = runBlocking {
    underTest.markTaskAsFinished(TASK_ID)

    verifyBlocking(carouselPersistenceService) { markTaskAsFinished(TASK_ID) }
  }

  // Tests for toUserCarouselTask conversion (tested indirectly through findActiveUserTask)
  @Test
  fun `SHOULD convert NEW state task entity to UserCarouselTask New`() = runBlocking {
    val taskEntity = createTaskEntity(state = NEW)
    carouselPersistenceService.mock({ findActiveCarouselTaskPerGame(USER_ID, GAME_ID) }, taskEntity)

    val result = underTest.findActiveUserTask(USER_ID, GAME_ID)

    // Since findActiveUserTask only returns InProgress tasks, we test conversion indirectly
    // by verifying that a NEW task returns null (meaning it was converted but filtered out)
    assertThat(result).isNull()
  }

  @Test
  fun `SHOULD convert UNCLAIMED state task entity correctly`() = runBlocking {
    val taskEntity = createTaskEntity(state = UNCLAIMED)
    carouselPersistenceService.mock({ getUserCarouselTask(TASK_ID) }, taskEntity)

    // Create a mock InProgress task to trigger updateTaskProgress
    val inProgressTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 50, null)
    carouselPersistenceService.mock({ updateTaskProgress(TASK_ID, 50, 100, null) }, true)

    val result = underTest.updateTaskProgress(inProgressTask, 100, null)

    assertThat(result).isInstanceOf(UserCarouselTask.Unclaimed::class)
    val unclaimedTask = result as UserCarouselTask.Unclaimed
    assertThat(unclaimedTask.taskId).isEqualTo(TASK_ID)
    assertThat(unclaimedTask.userId).isEqualTo(USER_ID)
    assertThat(unclaimedTask.definition).isEqualTo(taskDefinition)
  }

  @Test
  fun `SHOULD convert CLAIMED state task entity correctly`() = runBlocking {
    val taskEntity = createTaskEntity(state = CLAIMED, blockedUntil = BLOCKED_UNTIL)
    carouselPersistenceService.mock({ getUserCarouselTask(TASK_ID) }, taskEntity)

    // Create a mock InProgress task to trigger updateTaskProgress
    val inProgressTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 50, null)
    carouselPersistenceService.mock({ updateTaskProgress(TASK_ID, 50, 100, null) }, true)

    val result = underTest.updateTaskProgress(inProgressTask, 100, null)

    assertThat(result).isInstanceOf(UserCarouselTask.Claimed::class)
    val claimedTask = result as UserCarouselTask.Claimed
    assertThat(claimedTask.taskId).isEqualTo(TASK_ID)
    assertThat(claimedTask.userId).isEqualTo(USER_ID)
    assertThat(claimedTask.definition).isEqualTo(taskDefinition)
    assertThat(claimedTask.blockedUntil).isEqualTo(BLOCKED_UNTIL)
  }

  @Test
  fun `SHOULD convert COMPLETED state task entity correctly`() = runBlocking {
    val taskEntity = createTaskEntity(state = COMPLETED)
    carouselPersistenceService.mock({ getUserCarouselTask(TASK_ID) }, taskEntity)

    // Create a mock InProgress task to trigger updateTaskProgress
    val inProgressTask = UserCarouselTask.InProgress(TASK_ID, USER_ID, taskDefinition, 50, null)
    carouselPersistenceService.mock({ updateTaskProgress(TASK_ID, 50, 100, null) }, true)

    val result = underTest.updateTaskProgress(inProgressTask, 100, null)

    assertThat(result).isInstanceOf(UserCarouselTask.Completed::class)
    val completedTask = result as UserCarouselTask.Completed
    assertThat(completedTask.taskId).isEqualTo(TASK_ID)
    assertThat(completedTask.userId).isEqualTo(USER_ID)
    assertThat(completedTask.definition).isEqualTo(taskDefinition)
  }

  private fun createTaskEntity(
    state: UserCarouselTaskState,
    progress: Int = 0,
    achievement: String? = null,
    blockedUntil: Instant? = null,
  ) = UserCarouselTaskEntity(
    taskId = TASK_ID,
    userId = USER_ID,
    state = state,
    progress = progress,
    achievement = achievement,
    completedAt = null,
    blockedUntil = blockedUntil,
    taskDefinitionId = "task_def_123",
    gameId = taskDefinition.gameId,
    titleTranslation = taskDefinition.titleTranslation,
    icon = taskDefinition.icon,
    progressMax = taskDefinition.progressMax,
    goal = taskDefinition.goal,
    order = taskDefinition.order,
    calculator = taskDefinition.calculator,
    enabled = taskDefinition.enabled,
  )
}